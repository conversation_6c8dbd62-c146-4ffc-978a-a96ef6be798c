import { withNextVideo } from "next-video/process";
import { fetchRedirects } from "@/sanity/lib/fetch-redirects";
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  experimental: {
    //viewTransition: true,
  },
  images: {
    dangerouslyAllowSVG: true,
    contentDispositionType: "attachment",
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    remotePatterns: [
      {
        protocol: "https",
        hostname: "cdn.sanity.io",
        port: "",
      },
      {
        protocol: "https",
        hostname: "image.mux.com",
        port: "",
      },
      {
        protocol: "https",
        hostname: "placehold.co",
        port: "",
      },
      {
        protocol: "https",
        hostname: "images.autoscout24.ch",
        port: "",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
        port: "",
      },
    ],
  },
  async redirects() {
    return await fetchRedirects();
  },
};

export default withNextVideo(nextConfig, {
  provider: "mux",
  providerConfig: {
    mux: {
      generateAssetKey: undefined,
      videoQuality: "premium",
    },
  },
});
