"use client";

import { z } from "zod";
import type { FormType } from "@/types";
import toast from "react-hot-toast";
import { ArrowRight, AlertTriangle } from "lucide-react";
import { formatFieldId } from "@/lib/utils";
import { zodResolver } from "@hookform/resolvers/zod";
import type {
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { useForm } from "react-hook-form";
import { stegaClean } from "next-sanity";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Checkbox } from "../ui/checkbox";
import { RadioGroup, RadioGroupItem } from "../ui/radio-group";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";
import { Alert, AlertDescription } from "../ui/alert";
import React, { useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { FormErrorBoundary } from "./error-boundary";
import {
  validateFormConfiguration,
  formatValidationErrorsForCMS,
  sanitizeFieldOption,
  createSafeFieldName,
  type FormFieldType,
  type FormFieldOption,
} from "@/lib/form-validation";
import { CenterSelect } from "./form/CenterSelect";
import { ServiceSelect } from "./form/ServiceSelect";

type FormValues = Record<string, unknown>;

export default function Form({ form }: { form: FormType }) {
  const { fields, submitButtonText } = form;
  const searchParams = useSearchParams();

  // Validate and filter fields before processing
  const { validFields, errorMessages, hasErrors } = React.useMemo(() => {
    if (!fields) {
      return {
        validFields: [],
        errorMessages: ["No fields provided"],
        hasErrors: true,
      };
    }

    const result = validateFormConfiguration(fields);
    const formatted = formatValidationErrorsForCMS(result);

    return {
      validFields: result.validFields,
      errorMessages: formatted.items,
      hasErrors: formatted.hasErrors,
    };
  }, [fields]);

  const formSchema = z.object(
    validFields.reduce(
      (acc, field) => {
        const fieldName = createSafeFieldName(field.name);
        let validator: z.ZodTypeAny;

        // Handle different field types with safe fallbacks
        switch (field.inputType) {
          case "email":
            validator = z.string().email("Invalid email address");
            break;
          case "file":
            // File inputs return FileList, but we'll handle as unknown for flexibility
            validator = z.unknown();
            break;
          case "checkbox":
            validator = z.union([z.string(), z.array(z.string())]);
            break;
          case "date":
            validator = z
              .string()
              .refine((val) => !val || !isNaN(Date.parse(val)), {
                message: "Invalid date format",
              });
            break;
          case "time":
            validator = z
              .string()
              .refine(
                (val) => !val || /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.test(val),
                {
                  message: "Invalid time format (HH:MM)",
                },
              );
            break;
          case "text":
          case "textarea":
          case "tel":
          case "radio":
          case "select":
          case "custom":
          default:
            validator = z.string();
        }

        // Apply required validation with safe field name access
        if (field.isRequired) {
          const displayName = stegaClean(field.name) || "This field";

          if (field.inputType === "checkbox") {
            validator = validator.refine((val) => {
              if (Array.isArray(val)) return val.length > 0;
              return val && typeof val === "string" && val.length > 0;
            }, `${displayName} is required`);
          } else if (field.inputType === "file") {
            validator = validator.refine(
              (val) => val != null,
              `${displayName} is required`,
            );
          } else {
            if (validator instanceof z.ZodString) {
              validator = validator.min(1, `${displayName} is required`);
            }
          }
        }

        return { ...acc, [fieldName]: validator };
      },
      {} as Record<string, z.ZodTypeAny>,
    ),
  );

  type FormValues = z.infer<typeof formSchema>;

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    formState: { errors },
  } = useForm<FormValues>({
    resolver: zodResolver(formSchema),
  });

  // Set default values from URL parameters - only for valid fields
  useEffect(() => {
    if (validFields.length > 0) {
      validFields.forEach((field) => {
        const fieldName = createSafeFieldName(field.name);
        const urlParam = field.urlParameter;

        if (urlParam && searchParams.has(urlParam)) {
          const value = searchParams.get(urlParam);
          if (value) {
            try {
              setValue(fieldName, value);
            } catch (error) {
              console.warn(
                `Failed to set default value for field "${fieldName}":`,
                error,
              );
            }
          }
        }
      });
    }
  }, [validFields, searchParams, setValue]);

  const onSubmit = async (data: FormValues) => {
    try {
      console.log(data);
      const response = await fetch("/api/send", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          formId: form._id,
          formTitle: form.title,
          fields: data,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to send message");
      }

      await response.json();
      reset();
      toast.success("Message Sent");
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "An error occurred";
      toast.error(errorMessage);
    }
  };

  return (
    <FormErrorBoundary formTitle={form.title}>
      {/* Show field validation errors to CMS users */}
      {errorMessages.length > 0 && (
        <Alert
          variant={hasErrors ? "destructive" : "default"}
          className="mb-6 max-w-xl"
        >
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-2">
              <p className="font-medium">
                {hasErrors
                  ? "Form Configuration Errors"
                  : "Form Configuration Warnings"}
              </p>
              <p className="text-sm">
                {hasErrors
                  ? "The following errors prevent the form from working correctly:"
                  : "The following warnings were found (form will still work):"}
              </p>
              <ul className="text-sm space-y-1 ml-2">
                {errorMessages.map((error, index) => (
                  <li key={index} className="flex items-start gap-1">
                    <span className="mt-0.5">{error}</span>
                  </li>
                ))}
              </ul>
              <p className="text-sm text-muted-foreground mt-2">
                Please fix these issues in the CMS to ensure the form works
                correctly.
              </p>
            </div>
          </AlertDescription>
        </Alert>
      )}

      <form
        onSubmit={handleSubmit(onSubmit)}
        className="flex flex-col w-full max-w-xl p-6 md:p-8 space-y-6 border backdrop-blur-xs rounded-xl md:rounded-3xl bg-card"
      >
        {validFields.map((field, index) => {
          const fieldName = createSafeFieldName(field.name);
          const displayName = stegaClean(field.name) || `Field ${index + 1}`;

          return (
            <div key={fieldName} className="space-y-2">
              <label
                htmlFor={formatFieldId(fieldName)}
                className="text-sm font-medium"
              >
                {displayName}{" "}
                {field.isRequired && <span className="text-red-500">*</span>}
              </label>
              <FieldRenderer
                field={field}
                fieldName={fieldName}
                register={register}
                setValue={setValue}
                watch={watch}
                disabled={false}
              />
              {errors[fieldName] && (
                <p className="text-sm text-red-500">
                  {String(
                    errors[fieldName]?.message || "This field has an error",
                  )}
                </p>
              )}
            </div>
          );
        })}

        {validFields.length === 0 && errorMessages.length === 0 && (
          <Alert className="max-w-xl">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              No valid form fields found. Please add fields to this form in the
              CMS.
            </AlertDescription>
          </Alert>
        )}

        {validFields.length > 0 && (
          <Button variant="primary" type="submit" className="ml-auto">
            <span className="font-medium text-sm">
              {submitButtonText || "Submit"}
            </span>{" "}
            <ArrowRight
              size={16}
              className="group-hover:translate-x-1 transition-transform duration-300"
            />
          </Button>
        )}
      </form>
    </FormErrorBoundary>
  );
}

function FieldRenderer({
  field,
  fieldName,
  register,
  setValue,
  watch,
  disabled = false,
}: {
  field: FormFieldType;
  fieldName: string;
  register: UseFormRegister<FormValues>;
  setValue: UseFormSetValue<FormValues>;
  watch: UseFormWatch<FormValues>;
  disabled?: boolean;
}) {
  const fieldId = formatFieldId(fieldName);
  const placeholder = stegaClean(field.placeholder) || undefined;
  const options: FormFieldOption[] = field.options || [];

  // Wrap register calls in try-catch for additional safety
  const safeRegister = (name: string) => {
    try {
      return register(name);
    } catch (error) {
      console.error(`Failed to register field "${name}":`, error);
      return {};
    }
  };

  switch (field.inputType) {
    case "text":
    case "email":
    case "tel":
      return (
        <Input
          id={fieldId}
          {...safeRegister(fieldName)}
          type={field.inputType}
          placeholder={placeholder}
        />
      );

    case "textarea":
      return (
        <textarea
          id={fieldId}
          {...safeRegister(fieldName)}
          placeholder={placeholder}
          rows={4}
          className="w-full px-4 py-2 border rounded-lg bg-card resize-none"
        />
      );

    case "date":
      return (
        <Input
          id={fieldId}
          {...safeRegister(fieldName)}
          type="date"
          placeholder={placeholder}
        />
      );

    case "time":
      return (
        <Input
          id={fieldId}
          {...safeRegister(fieldName)}
          type="time"
          placeholder={placeholder}
        />
      );

    case "file":
      return (
        <Input
          id={fieldId}
          {...safeRegister(fieldName)}
          type="file"
          className="file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary file:text-primary-foreground hover:file:bg-primary/80"
        />
      );

    case "select":
      return (
        <Select
          onValueChange={(value) => {
            try {
              setValue(fieldName, value);
            } catch (error) {
              console.error(
                `Failed to set value for select field "${fieldName}":`,
                error,
              );
            }
          }}
          defaultValue={watch(fieldName) as string}
        >
          <SelectTrigger id={fieldId} className="w-full">
            <SelectValue placeholder={placeholder || "Select an option"} />
          </SelectTrigger>
          <SelectContent>
            {options.map((option, index) => {
              const sanitized = sanitizeFieldOption(option, index);

              return (
                <SelectItem key={index} value={sanitized.value}>
                  {sanitized.label}
                </SelectItem>
              );
            })}
          </SelectContent>
        </Select>
      );

    case "radio":
      return (
        <RadioGroup
          onValueChange={(value) => {
            try {
              setValue(fieldName, value);
            } catch (error) {
              console.error(
                `Failed to set value for radio field "${fieldName}":`,
                error,
              );
            }
          }}
          defaultValue={watch(fieldName) as string}
          className="flex flex-col space-y-2"
        >
          {options.map((option, index) => {
            const sanitized = sanitizeFieldOption(option, index);

            return (
              <div key={index} className="flex items-center space-x-2">
                <RadioGroupItem
                  value={sanitized.value}
                  id={`${fieldId}-${index}`}
                />
                <label
                  htmlFor={`${fieldId}-${index}`}
                  className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                >
                  {sanitized.label}
                </label>
              </div>
            );
          })}
        </RadioGroup>
      );

    case "checkbox":
      if (options.length > 1) {
        // Multiple checkboxes
        return (
          <div className="flex flex-col space-y-2">
            {options.map((option, index) => {
              const sanitized = sanitizeFieldOption(option, index);
              const currentValues = watch(fieldName) || [];
              const isChecked = Array.isArray(currentValues)
                ? currentValues.includes(sanitized.value)
                : currentValues === sanitized.value;

              return (
                <div key={index} className="flex items-center space-x-2">
                  <Checkbox
                    id={`${fieldId}-${index}`}
                    checked={isChecked}
                    onCheckedChange={(checked) => {
                      try {
                        const current = watch(fieldName) || [];
                        const currentArray = Array.isArray(current)
                          ? (current as string[])
                          : [];

                        if (checked) {
                          setValue(fieldName, [
                            ...currentArray,
                            sanitized.value,
                          ]);
                        } else {
                          setValue(
                            fieldName,
                            currentArray.filter(
                              (v: string) => v !== sanitized.value,
                            ),
                          );
                        }
                      } catch (error) {
                        console.error(
                          `Failed to update checkbox value for "${fieldName}":`,
                          error,
                        );
                      }
                    }}
                  />
                  <label
                    htmlFor={`${fieldId}-${index}`}
                    className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {sanitized.label}
                  </label>
                </div>
              );
            })}
          </div>
        );
      } else {
        // Single checkbox
        const option = options[0];
        const sanitized = option
          ? sanitizeFieldOption(option, 0)
          : { label: placeholder || "Check this option", value: "true" };

        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              id={fieldId}
              checked={!!watch(fieldName)}
              onCheckedChange={(checked) => {
                try {
                  setValue(fieldName, checked ? sanitized.value : "");
                } catch (error) {
                  console.error(
                    `Failed to update single checkbox value for "${fieldName}":`,
                    error,
                  );
                }
              }}
            />
            <label
              htmlFor={fieldId}
              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
            >
              {sanitized.label}
            </label>
          </div>
        );
      }

    case "custom":
      // Custom fields need special handling based on their _key
      // Since _key is not part of the FormFieldType, we'll cast to access it
      const customField = field as FormFieldType & { _key?: string };
      switch (customField._key) {
        case "4f492b3f57f7":
          // Center Select Component
          return (
            <CenterSelect
              fieldName={fieldName}
              placeholder={placeholder || "Select a center"}
              value={watch(fieldName) as string}
              onChange={(value: string) => {
                try {
                  setValue(fieldName, value);
                } catch (error) {
                  console.error(
                    `Failed to set value for center select field "${fieldName}":`,
                    error,
                  );
                }
              }}
              disabled={disabled}
              required={field.isRequired}
              mergeByAddress={true}
            />
          );
        case "764428ee98a9":
          // Service Select Component
          return (
            <ServiceSelect
              fieldName={fieldName}
              placeholder={placeholder || "Select a service"}
              value={watch(fieldName) as string}
              onChange={(value: string) => {
                try {
                  setValue(fieldName, value);
                } catch (error) {
                  console.error(
                    `Failed to set value for service select field "${fieldName}":`,
                    error,
                  );
                }
              }}
              disabled={disabled}
              required={field.isRequired}
              excludeB2B={true}
            />
          );
        default:
          break;
      }
      // For custom fields, render a placeholder that can be replaced with custom logic
      return (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              <p>Custom field implementation needed</p>
              <p className="text-xs text-muted-foreground">
                Field Key: {customField._key || "Unknown"}
              </p>
            </div>
          </AlertDescription>
        </Alert>
      );

    default:
      return (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Unsupported field type: {field.inputType || "Unknown"}
          </AlertDescription>
        </Alert>
      );
  }
}
