"use client";

import React, { useEffect, useState } from "react";
import { client } from "@/sanity/lib/client";
import { centersForFormSelectQuery } from "@/sanity/lib/queries/form-selects";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Loader2 } from "lucide-react";
import type {
  CenterSelectProps,
  CenterSelectData,
  MergedCenter,
  SelectOption,
} from "./types";

/**
 * Utility function to create a normalized address string for comparison
 */
function normalizeAddress(address: CenterSelectData["address"]): string {
  const parts = [
    address.street?.trim(),
    address.postalCode?.trim(),
    address.city?.trim(),
    address.region?.trim(),
    address.country?.trim(),
  ].filter(Boolean);

  return parts.join(", ").toLowerCase();
}

/**
 * Utility function to format address for display
 */
function formatAddressForDisplay(address: CenterSelectData["address"]): string {
  const parts = [address.street, address.city, address.postalCode].filter(
    Boolean,
  );

  return parts.join(", ");
}

/**
 * Merges centers that share the same address
 */
function mergeCentersByAddress(centers: CenterSelectData[]): MergedCenter[] {
  const addressMap = new Map<string, CenterSelectData[]>();

  // Group centers by normalized address
  centers.forEach((center) => {
    const normalizedAddress = normalizeAddress(center.address);
    if (!addressMap.has(normalizedAddress)) {
      addressMap.set(normalizedAddress, []);
    }
    addressMap.get(normalizedAddress)!.push(center);
  });

  // Create merged centers
  const mergedCenters: MergedCenter[] = [];

  addressMap.forEach((centersAtAddress) => {
    if (centersAtAddress.length === 1) {
      // Single center at this address
      const center = centersAtAddress[0];
      mergedCenters.push({
        id: center._id,
        name: center.name,
        centerType: center.centerType,
        address: formatAddressForDisplay(center.address),
        centerIds: [center._id],
        autoscoutSellerIds: center.autoscoutSellerId
          ? [center.autoscoutSellerId]
          : [],
      });
    } else {
      // Multiple centers at same address - merge them
      const centerTypes = [
        ...new Set(centersAtAddress.map((c) => c.centerType)),
      ];
      const centerNames = centersAtAddress.map((c) => c.name);
      const centerIds = centersAtAddress.map((c) => c._id);
      const autoscoutSellerIds = centersAtAddress
        .map((c) => c.autoscoutSellerId)
        .filter(Boolean) as string[];

      // Create a combined ID and name
      const combinedId = centerIds.join("|");
      const combinedName =
        centerNames.length > 1
          ? `${centerNames.join(" & ")} (${centerTypes.join(", ")})`
          : centerNames[0];

      mergedCenters.push({
        id: combinedId,
        name: combinedName,
        centerType: centerTypes.join(", "),
        address: formatAddressForDisplay(centersAtAddress[0].address),
        centerIds,
        autoscoutSellerIds,
      });
    }
  });

  return mergedCenters.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * CenterSelect Component
 *
 * Fetches centers from Sanity and provides a select interface.
 * Optionally merges centers that share the same address.
 */
export function CenterSelect({
  fieldName,
  placeholder = "Select a center",
  value,
  onChange,
  disabled = false,
  required = false,
  mergeByAddress = true,
}: CenterSelectProps) {
  const [centers, setCenters] = useState<CenterSelectData[]>([]);
  const [mergedCenters, setMergedCenters] = useState<MergedCenter[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCenters() {
      try {
        setLoading(true);
        setError(null);

        const data = await client.fetch(centersForFormSelectQuery);

        if (!data || !Array.isArray(data)) {
          throw new Error("Invalid data format received from Sanity");
        }

        setCenters(data);

        if (mergeByAddress) {
          const merged = mergeCentersByAddress(data);
          setMergedCenters(merged);
        }
      } catch (err) {
        console.error("Error fetching centers:", err);
        setError(err instanceof Error ? err.message : "Failed to load centers");
      } finally {
        setLoading(false);
      }
    }

    fetchCenters();
  }, [mergeByAddress]);

  // Convert data to select options
  const options: SelectOption[] = React.useMemo(() => {
    const dataToUse = mergeByAddress
      ? mergedCenters
      : centers.map((c) => ({
          id: c._id,
          name: c.name,
          centerType: c.centerType,
          address: formatAddressForDisplay(c.address),
          centerIds: [c._id],
          autoscoutSellerIds: c.autoscoutSellerId ? [c.autoscoutSellerId] : [],
        }));

    return dataToUse.map((item) => ({
      value: item.id,
      label: `${item.name} - ${item.address}`,
    }));
  }, [centers, mergedCenters, mergeByAddress]);

  if (loading) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded-lg bg-muted/50">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">
          Loading centers...
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-1">
            <p>Failed to load centers</p>
            <p className="text-xs">{error}</p>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (options.length === 0) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No centers available. Please add centers in the CMS.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Select
      value={value}
      onValueChange={onChange}
      disabled={disabled}
      required={required}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
