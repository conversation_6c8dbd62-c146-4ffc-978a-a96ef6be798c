"use client";

import React, { useEffect, useState } from "react";
import { client } from "@/sanity/lib/client";
import { centersForFormSelectQuery } from "@/sanity/lib/queries/form-selects";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Loader2 } from "lucide-react";
import type {
  CenterSelectProps,
  CenterSelectData,
  MergedCenter,
  SelectOption,
} from "./types";

/**
 * Utility function to create a normalized address string for comparison
 */
function normalizeAddress(address: CenterSelectData["address"]): string {
  // Create a normalized key based on street, city, and postal code
  // Remove extra spaces and convert to lowercase for comparison
  const street = address.street?.trim().toLowerCase().replace(/\s+/g, " ") || "";
  const city = address.city?.trim().toLowerCase().replace(/\s+/g, " ") || "";
  const postalCode = address.postalCode?.trim().toLowerCase() || "";

  // Use street + city + postal code as the unique identifier
  return `${street}|${city}|${postalCode}`;
}

/**
 * Utility function to format address for display
 */
function formatAddressForDisplay(address: CenterSelectData["address"]): string {
  const parts = [address.street, address.city, address.postalCode].filter(
    Boolean,
  );

  return parts.join(", ");
}

/**
 * Merges centers that share the same address
 */
function mergeCentersByAddress(centers: CenterSelectData[]): MergedCenter[] {
  const addressMap = new Map<string, CenterSelectData[]>();

  // Group centers by normalized address
  centers.forEach((center) => {
    const normalizedAddress = normalizeAddress(center.address);
    console.log(`Center: ${center.name}, Normalized Address: ${normalizedAddress}`);
    if (normalizedAddress) { // Only process centers with valid addresses
      if (!addressMap.has(normalizedAddress)) {
        addressMap.set(normalizedAddress, []);
      }
      addressMap.get(normalizedAddress)!.push(center);
    }
  });

  // Create merged centers
  const mergedCenters: MergedCenter[] = [];

  addressMap.forEach((centersAtAddress) => {
    if (centersAtAddress.length === 1) {
      // Single center at this address
      const center = centersAtAddress[0];
      mergedCenters.push({
        id: center._id,
        name: center.name,
        centerType: center.centerType,
        address: formatAddressForDisplay(center.address),
        centerIds: [center._id],
        autoscoutSellerIds: center.autoscoutSellerId
          ? [center.autoscoutSellerId]
          : [],
      });
    } else {
      // Multiple centers at same address - merge them
      // Sort centers to prefer non-occasion centers first
      const sortedCenters = centersAtAddress.sort((a, b) => {
        const aIsOccasion = a.name.toLowerCase().includes("occasion");
        const bIsOccasion = b.name.toLowerCase().includes("occasion");

        // Non-occasion centers come first
        if (aIsOccasion && !bIsOccasion) return 1;
        if (!aIsOccasion && bIsOccasion) return -1;

        // Then sort by center type (audi, skoda, etc.)
        return a.centerType.localeCompare(b.centerType);
      });

      const centerTypes = [...new Set(sortedCenters.map(c => c.centerType))];
      const centerIds = sortedCenters.map(c => c._id);
      const autoscoutSellerIds = sortedCenters
        .map(c => c.autoscoutSellerId)
        .filter(Boolean) as string[];

      // Use the preferred center (first in sorted list) as the primary name
      const primaryCenter = sortedCenters[0];
      const combinedId = centerIds.join("|");

      // Create a clean name - prefer the non-occasion center name
      let displayName = primaryCenter.name;

      // If we have multiple different center types, show them
      if (centerTypes.length > 1) {
        displayName = `${primaryCenter.name.replace(/\s*occasions?\s*/i, "").trim()} (${centerTypes.join(", ")})`;
      }

      mergedCenters.push({
        id: combinedId,
        name: displayName,
        centerType: centerTypes.join(", "),
        address: formatAddressForDisplay(primaryCenter.address),
        centerIds,
        autoscoutSellerIds,
      });
    }
  });

  return mergedCenters.sort((a, b) => a.name.localeCompare(b.name));
}

/**
 * CenterSelect Component
 *
 * Fetches centers from Sanity and provides a select interface.
 * Optionally merges centers that share the same address.
 */
export function CenterSelect({
  fieldName,
  placeholder = "Select a center",
  value,
  onChange,
  disabled = false,
  required = false,
  mergeByAddress = true,
}: CenterSelectProps) {
  const [centers, setCenters] = useState<CenterSelectData[]>([]);
  const [mergedCenters, setMergedCenters] = useState<MergedCenter[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCenters() {
      try {
        setLoading(true);
        setError(null);

        const data = await client.fetch(centersForFormSelectQuery);

        if (!data || !Array.isArray(data)) {
          throw new Error("Invalid data format received from Sanity");
        }

        setCenters(data);

        if (mergeByAddress) {
          const merged = mergeCentersByAddress(data);
          setMergedCenters(merged);
        }
      } catch (err) {
        console.error("Error fetching centers:", err);
        setError(err instanceof Error ? err.message : "Failed to load centers");
      } finally {
        setLoading(false);
      }
    }

    fetchCenters();
  }, [mergeByAddress]);

  // Convert data to select options
  const options: SelectOption[] = React.useMemo(() => {
    const dataToUse = mergeByAddress
      ? mergedCenters
      : centers.map((c) => ({
          id: c._id,
          name: c.name,
          centerType: c.centerType,
          address: formatAddressForDisplay(c.address),
          centerIds: [c._id],
          autoscoutSellerIds: c.autoscoutSellerId ? [c.autoscoutSellerId] : [],
        }));

    return dataToUse.map((item) => ({
      value: item.id,
      label: `${item.name} - ${item.address}`,
    }));
  }, [centers, mergedCenters, mergeByAddress]);

  if (loading) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded-lg bg-muted/50">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">
          Loading centers...
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-1">
            <p>Failed to load centers</p>
            <p className="text-xs">{error}</p>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (options.length === 0) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No centers available. Please add centers in the CMS.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Select
      value={value}
      onValueChange={onChange}
      disabled={disabled}
      required={required}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
