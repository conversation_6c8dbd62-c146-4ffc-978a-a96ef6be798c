"use client";

import React, { useEffect, useState } from "react";
import { client } from "@/sanity/lib/client";
import { centersForFormSelectQuery } from "@/sanity/lib/queries/form-selects";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Loader2 } from "lucide-react";
import { stegaClean } from "next-sanity";
import type {
  CenterSelectProps,
  CenterSelectData,
  SelectOption,
} from "./types";

/**
 * Groups centers by brand and filters out occasion centers
 */
function groupCentersByBrand(centers: CenterSelectData[]): SelectOption[] {
  // Filter out centers with "occasion" in the name
  const filteredCenters = centers.filter(center => {
    const name = stegaClean(center.name) || "";
    return !name.toLowerCase().includes("occasion");
  });

  // Convert to select options, sorted by name
  const options: SelectOption[] = filteredCenters.map(center => {
    const name = stegaClean(center.name) || "";
    return {
      value: name, // Use name as value (what gets sent in form)
      label: name, // Display name
    };
  });

  // Sort options by label
  return options.sort((a, b) => a.label.localeCompare(b.label));
}

/**
 * CenterSelect Component
 *
 * Fetches centers from Sanity, groups by brand, and hides occasion centers.
 * Returns center names (not IDs) as form values.
 */
export function CenterSelect({
  fieldName,
  placeholder = "Select a center",
  value,
  onChange,
  disabled = false,
  required = false,
}: CenterSelectProps) {
  const [centers, setCenters] = useState<CenterSelectData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCenters() {
      try {
        setLoading(true);
        setError(null);

        const data = await client.fetch(centersForFormSelectQuery);

        if (!data || !Array.isArray(data)) {
          throw new Error("Invalid data format received from Sanity");
        }

        setCenters(data);
      } catch (err) {
        console.error("Error fetching centers:", err);
        setError(err instanceof Error ? err.message : "Failed to load centers");
      } finally {
        setLoading(false);
      }
    }

    fetchCenters();
  }, []);

  // Convert data to select options
  const options: SelectOption[] = React.useMemo(() => {
    return groupCentersByBrand(centers);
  }, [centers]);