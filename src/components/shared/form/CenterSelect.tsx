"use client";

import React, { useEffect, useState } from "react";
import { client } from "@/sanity/lib/client";
import { centersForFormSelectQuery } from "@/sanity/lib/queries/form-selects";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Loader2 } from "lucide-react";
import { stegaClean } from "next-sanity";
import type {
  CenterSelectProps,
  CenterSelectData,
  SelectOption,
} from "./types";

/**
 * Formats address for display
 */
function formatAddressForDisplay(address: CenterSelectData["address"]): string {
  const parts = [
    stegaClean(address.street),
    stegaClean(address.city),
    stegaClean(address.postalCode)
  ].filter(Boolean);

  return parts.join(", ");
}

/**
 * Groups centers by brand and filters out occasion centers
 */
function groupCentersByBrand(centers: CenterSelectData[]): SelectOption[] {
  // Filter out centers with "occasion" in the name
  const filteredCenters = centers.filter((center) => {
    const name = stegaClean(center.name) || "";
    return !name.toLowerCase().includes("occasion");
  });

  // Convert to select options, sorted by name
  const options: SelectOption[] = filteredCenters.map((center) => {
    const name = stegaClean(center.name) || "";
    const address = formatAddressForDisplay(center.address);
    return {
      value: name, // Use name as value (what gets sent in form)
      label: `${name} - ${address}`, // Display name with address
    };
  });

  // Sort options by label
  return options.sort((a, b) => a.label.localeCompare(b.label));
}

/**
 * CenterSelect Component
 *
 * Fetches centers from Sanity, groups by brand, and hides occasion centers.
 * Returns center names (not IDs) as form values.
 */
export function CenterSelect({
  //fieldName,
  placeholder = "Select a center",
  value,
  onChange,
  disabled = false,
  required = false,
}: CenterSelectProps) {
  const [centers, setCenters] = useState<CenterSelectData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchCenters() {
      try {
        setLoading(true);
        setError(null);

        const data = await client.fetch(centersForFormSelectQuery);

        if (!data || !Array.isArray(data)) {
          throw new Error("Invalid data format received from Sanity");
        }

        setCenters(data);
      } catch (err) {
        console.error("Error fetching centers:", err);
        setError(err instanceof Error ? err.message : "Failed to load centers");
      } finally {
        setLoading(false);
      }
    }

    fetchCenters();
  }, []);

  // Convert data to select options
  const options: SelectOption[] = React.useMemo(() => {
    return groupCentersByBrand(centers);
  }, [centers]);

  if (loading) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded-lg bg-muted/50">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">
          Loading centers...
        </span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-1">
            <p>Failed to load centers</p>
            <p className="text-xs">{error}</p>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (options.length === 0) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No centers available. Please add centers in the CMS.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Select
      value={value}
      onValueChange={onChange}
      disabled={disabled}
      required={required}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
