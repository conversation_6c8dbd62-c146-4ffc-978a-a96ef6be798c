// Types for custom form select components

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface CenterSelectData {
  _id: string;
  name: string;
  centerType: "audi" | "multibrands" | "occasions" | "skoda";
  address: {
    street?: string;
    city?: string;
    postalCode?: string;
    region?: string;
    country?: string;
  };
  autoscoutSellerId?: string;
}

export interface ServiceSelectData {
  _id: string;
  title: string;
  slug: string;
  shortDescription?: string;
  serviceScope: "consumer" | "b2b";
}

export interface BaseSelectProps {
  fieldName: string;
  placeholder?: string;
  value?: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  required?: boolean;
}

export interface CenterSelectProps extends BaseSelectProps {
  mergeByAddress?: boolean;
}

export interface ServiceSelectProps extends BaseSelectProps {
  excludeB2B?: boolean;
}

// Merged center type for display
export interface MergedCenter {
  id: string; // Combined ID for merged centers
  name: string; // Combined name for merged centers
  centerType: string; // Combined types
  address: string; // Formatted address string
  centerIds: string[]; // Array of original center IDs
  autoscoutSellerIds: string[]; // Array of seller IDs
}
