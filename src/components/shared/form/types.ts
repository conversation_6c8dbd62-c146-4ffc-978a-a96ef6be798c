// Types for custom form select components

export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
  address?: string; // Optional address for center select
}

export interface CenterSelectData {
  _id: string;
  name: string;
  centerType: "audi" | "multibrands" | "occasions" | "skoda";
  address: {
    street?: string;
    city?: string;
    postalCode?: string;
    region?: string;
    country?: string;
  };
  autoscoutSellerId?: string;
  brandLogo?: {
    asset?: {
      url?: string;
    };
    altText?: string;
  };
}

export interface ServiceSelectData {
  _id: string;
  title: string;
  slug: string;
  shortDescription?: string;
  serviceScope: "consumer" | "b2b";
}

export interface BaseSelectProps {
  fieldName: string;
  placeholder?: string;
  value?: string;
  onChange: (value: string) => void;
  disabled?: boolean;
  required?: boolean;
}

export type CenterSelectProps = BaseSelectProps;

export interface ServiceSelectProps extends BaseSelectProps {
  excludeB2B?: boolean;
}
