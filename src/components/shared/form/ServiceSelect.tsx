"use client";

import React, { useEffect, useState } from "react";
import { sanityFetch } from "@/sanity/lib/live";
import { consumerServicesForFormSelectQuery } from "@/sanity/lib/queries/form-selects";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertTriangle, Loader2 } from "lucide-react";
import type {
  ServiceSelectProps,
  ServiceSelectData,
  SelectOption,
} from "./types";

/**
 * ServiceSelect Component
 * 
 * Fetches services from Sanity and provides a select interface.
 * Filters out B2B services by default, showing only consumer services.
 */
export function ServiceSelect({
  fieldName,
  placeholder = "Select a service",
  value,
  onChange,
  disabled = false,
  required = false,
  excludeB2B = true,
}: ServiceSelectProps) {
  const [services, setServices] = useState<ServiceSelectData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchServices() {
      try {
        setLoading(true);
        setError(null);
        
        const { data } = await sanityFetch({
          query: consumerServicesForFormSelectQuery,
        });
        
        if (!data || !Array.isArray(data)) {
          throw new Error("Invalid data format received from Sanity");
        }
        
        // Additional client-side filtering if needed
        let filteredServices = data;
        if (excludeB2B) {
          filteredServices = data.filter(service => service.serviceScope !== "b2b");
        }
        
        setServices(filteredServices);
      } catch (err) {
        console.error("Error fetching services:", err);
        setError(err instanceof Error ? err.message : "Failed to load services");
      } finally {
        setLoading(false);
      }
    }

    fetchServices();
  }, [excludeB2B]);

  // Convert services to select options
  const options: SelectOption[] = React.useMemo(() => {
    return services.map((service) => ({
      value: service._id,
      label: service.title,
    }));
  }, [services]);

  if (loading) {
    return (
      <div className="flex items-center gap-2 p-3 border rounded-lg bg-muted/50">
        <Loader2 className="h-4 w-4 animate-spin" />
        <span className="text-sm text-muted-foreground">Loading services...</span>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          <div className="space-y-1">
            <p>Failed to load services</p>
            <p className="text-xs">{error}</p>
          </div>
        </AlertDescription>
      </Alert>
    );
  }

  if (options.length === 0) {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          No consumer services available. Please add consumer services in the CMS.
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <Select
      value={value}
      onValueChange={onChange}
      disabled={disabled}
      required={required}
    >
      <SelectTrigger className="w-full">
        <SelectValue placeholder={placeholder} />
      </SelectTrigger>
      <SelectContent>
        {options.map((option) => (
          <SelectItem key={option.value} value={option.value}>
            {option.label}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
