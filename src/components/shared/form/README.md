# Form Select Components

This directory contains custom select components for forms that integrate with Sanity CMS.

## Components

### CenterSelect

A select component that fetches centers from Sanity and provides options for form selection.

**Features:**
- Fetches centers from Sanity CMS
- Merges centers with the same address to avoid duplicates
- Displays centers with formatted address information
- Loading and error states
- TypeScript support

**Usage:**
```tsx
import { CenterSelect } from "@/components/shared/form";

<CenterSelect
  fieldName="center"
  placeholder="Select a center"
  value={selectedCenter}
  onChange={handleCenterChange}
  mergeByAddress={true}
  required={true}
/>
```

**Props:**
- `fieldName`: The form field name
- `placeholder`: Placeholder text for the select
- `value`: Current selected value
- `onChange`: Callback when selection changes
- `disabled`: Whether the select is disabled
- `required`: Whether the field is required
- `mergeByAddress`: Whether to merge centers with same address (default: true)

### ServiceSelect

A select component that fetches services from Sanity, filtering out B2B services by default.

**Features:**
- Fetches services from Sanity CMS
- Filters out B2B services (shows only consumer services)
- Loading and error states
- TypeScript support

**Usage:**
```tsx
import { ServiceSelect } from "@/components/shared/form";

<ServiceSelect
  fieldName="service"
  placeholder="Select a service"
  value={selectedService}
  onChange={handleServiceChange}
  excludeB2B={true}
  required={true}
/>
```

**Props:**
- `fieldName`: The form field name
- `placeholder`: Placeholder text for the select
- `value`: Current selected value
- `onChange`: Callback when selection changes
- `disabled`: Whether the select is disabled
- `required`: Whether the field is required
- `excludeB2B`: Whether to exclude B2B services (default: true)

## Integration with Form System

These components are automatically integrated into the main form system when using custom fields with specific `_key` values:

- `4f492b3f57f7`: Renders CenterSelect component
- `764428ee98a9`: Renders ServiceSelect component

## Data Sources

### Centers Query
The CenterSelect component uses the `centersForFormSelectQuery` which fetches:
- Center ID, name, and type
- Address information for merging logic
- AutoScout seller ID

### Services Query
The ServiceSelect component uses the `consumerServicesForFormSelectQuery` which fetches:
- Service ID, title, and slug
- Service scope (filters to consumer services only)
- Short description

## Address Merging Logic

The CenterSelect component includes sophisticated address merging:

1. **Normalization**: Addresses are normalized by combining street, postal code, city, region, and country into a lowercase string
2. **Grouping**: Centers with identical normalized addresses are grouped together
3. **Merging**: Multiple centers at the same address are combined with:
   - Combined names (e.g., "Audi Lutry & Skoda Lutry")
   - Combined center types in parentheses
   - All original center IDs preserved
   - All AutoScout seller IDs preserved

## Error Handling

Both components include comprehensive error handling:
- Loading states with spinner
- Error alerts with descriptive messages
- Empty state handling
- Graceful fallbacks for missing data

## TypeScript Support

All components are fully typed with:
- Proper interface definitions
- Generic type support
- Strict type checking
- IntelliSense support
