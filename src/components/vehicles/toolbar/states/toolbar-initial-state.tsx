"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { VehicleCountButton } from "../components/vehicle-count-button";
import type { FilterContextType } from "@/components/vehicles/filters/types";
import type { FilterTypeValue } from "@/lib/api/autoscout/const/filters";
import { FILTER_TYPES } from "@/lib/api/autoscout/const/filters";
import { cn } from "@/lib/utils";
import { Schemas } from "@/lib/api/autoscout/types/generated";
import ListingQuery = Schemas.ListingQuery;
import type { VehicleSearchToolbarOverlayProps } from "@/components/vehicles/toolbar/vehicle-search-toolbar-overlay";

interface ToolbarInitialStateProps {
  config: {
    availableFilters: FilterTypeValue[];
    popularFilters: VehicleSearchToolbarOverlayProps["config"]["popularFilters"];
    showVehicleCount: boolean;
  };
  context: FilterContextType;
  onFilterClick: (filterType: FilterTypeValue) => void;
  onPresetClick: (preset: { filters: ListingQuery | null }) => void;
  onPreview: () => void;
}

const FILTER_LABELS: Record<FilterTypeValue, string> = {
  [FILTER_TYPES.MAKE]: "Marque",
  [FILTER_TYPES.PRICE]: "Prix",
  [FILTER_TYPES.CONDITION]: "Condition",
  [FILTER_TYPES.MILEAGE]: "Kilométrage",
  [FILTER_TYPES.FUEL]: "Carburant",
  [FILTER_TYPES.YEAR]: "Année",
  [FILTER_TYPES.MODEL]: "Modèle",
  [FILTER_TYPES.TRANSMISSION]: "Transmission",
  [FILTER_TYPES.BODY_TYPE]: "Carrosserie",
  [FILTER_TYPES.DRIVE_TYPE]: "Traction",
  [FILTER_TYPES.COLOR]: "Couleur",
  [FILTER_TYPES.EMISSION]: "Émission",
};

export function ToolbarInitialState({
  config,
  context,
  onFilterClick,
  onPresetClick,
  onPreview,
}: ToolbarInitialStateProps) {
  const hasActiveFilter = (filterType: FilterTypeValue): boolean => {
    // Check if this filter type has active values in context
    switch (filterType) {
      case FILTER_TYPES.MAKE:
        return !!context.currentQuery.makeModelVersions?.length;
      case FILTER_TYPES.PRICE:
        return !!(
          context.currentQuery.priceFrom || context.currentQuery.priceTo
        );
      case FILTER_TYPES.CONDITION:
        return !!context.currentQuery.conditionTypes?.length;
      case FILTER_TYPES.MILEAGE:
        return !!(
          context.currentQuery.mileageFrom || context.currentQuery.mileageTo
        );
      case FILTER_TYPES.FUEL:
        return !!context.currentQuery.fuelTypes?.length;
      case FILTER_TYPES.YEAR:
        return !!(
          context.currentQuery.firstRegistrationYearFrom ||
          context.currentQuery.firstRegistrationYearTo
        );
      default:
        return false;
    }
  };

  return (
    <div className="space-y-6">
      {/* Filter Cards Grid */}
      <div className="grid grid-cols-3 gap-4 min-h-50">
        {config.availableFilters.map((filterType) => (
          <button
            key={filterType}
            onClick={() => onFilterClick(filterType)}
            className={cn(
              "cursor-pointer bg-card/70 hover:bg-accent rounded-lg p-6 text-center transition-colors",
              hasActiveFilter(filterType) && "ring-2 ring-foreground  bg-card",
            )}
          >
            <span className="text-xs lg:text-lg">
              {FILTER_LABELS[filterType]}
            </span>
          </button>
        ))}
      </div>

      {/* Popular Filters Section */}
      {config.popularFilters && config.popularFilters.length > 0 && (
        <div>
          <h3 className="text-xl font-semibold mb-4">Filtres populaires</h3>
          <div className="flex gap-3 overflow-x-auto py-2">
            {config.popularFilters.map((preset, index) => {
              const normalizedPreset = {
                label: preset.label,
                filters:
                  preset.filters &&
                  (Object.fromEntries(
                    Object.entries(preset.filters).filter(
                      // eslint-disable-next-line @typescript-eslint/no-unused-vars
                      ([_, value]) => value !== null,
                    ),
                  ) as ListingQuery),
              };
              return (
                <Button
                  key={index}
                  variant="secondary"
                  onClick={() => onPresetClick(normalizedPreset)}
                  className="bg-card hover:bg-accent"
                >
                  {preset.label}
                </Button>
              );
            })}
          </div>
        </div>
      )}

      {/* Action Button */}
      <div className="flex justify-end">
        <VehicleCountButton
          context={context}
          onClick={onPreview}
          showCount={config.showVehicleCount}
        />
      </div>
    </div>
  );
}
