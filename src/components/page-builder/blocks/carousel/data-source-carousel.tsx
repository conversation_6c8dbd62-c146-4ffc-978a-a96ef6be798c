"use client";

import React from "react";
import { motion } from "motion/react";
import { Filter } from "lucide-react";
import Container from "@/components/global/container";
import { useDataSource } from "@/hooks/useDataSource";
import { CarouselImplementation } from "./carousel-implementation";
import type { DataSourceCarouselProps, CarouselSlide } from "./types";
import type { DataSourceConfig } from "@/lib/data-source-fetcher";

/**
 * DataSourceCarousel Component
 *
 * Fetches data from an external source and renders it in a carousel
 */
export function DataSourceCarousel({
  dataSource,
  carouselConfig,
  title,
  description,
  anchorId,
}: DataSourceCarouselProps) {
  const { items, loading, error } = useDataSource(
    dataSource as DataSourceConfig,
  );

  // Convert GridItems to CarouselSlides
  const slides = items.map((item) => {
    const slide = {
      _key: item._key,
      _type: "cardSlide",
      title: item.title || item.name || "",
      description:
        typeof item.description === "string"
          ? item.description
          : item.shortDescription || "",
      image: item.image as CarouselSlide["image"],
      button: item.link
        ? {
            text: item.link.label || "Learn More",
            url: item.link.url,
            style: "primary",
          }
        : null,
    };
    return slide;
  });

  // Loading state
  if (loading) {
    return (
      <section
        {...(anchorId ? { id: anchorId } : {})}
        className="px-4 md:px-10 py-16 md:py-24 pattern-bg"
      >
        <Container>
          <div className="flex justify-center items-center py-16 min-h-[300px]">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </Container>
      </section>
    );
  }

  // Error state
  if (error) {
    return (
      <section
        {...(anchorId ? { id: anchorId } : {})}
        className="px-4 md:px-10 py-16 md:py-24 pattern-bg"
      >
        <Container>
          <motion.div
            className="bg-red-50 border border-dashed rounded-3xl p-6 text-center"
            initial={{ opacity: 0, scale: 0.95 }}
            whileInView={{ opacity: 1, scale: 1 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-red-600 mb-2">⚠️ Content Loading Error</div>
            <p className="text-red-700">{error}</p>
          </motion.div>
        </Container>
      </section>
    );
  }

  // Empty state
  if (slides.length === 0) {
    return (
      <section
        {...(anchorId ? { id: anchorId } : {})}
        className="px-4 md:px-10 py-16 md:py-24 pattern-bg"
      >
        <Container>
          <motion.div
            className="text-center py-12"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ duration: 0.6 }}
          >
            <div className="text-gray-400 mb-4">
              <Filter className="w-12 h-12 mx-auto" />
            </div>
            <h3 className="text-lg font-medium text-card-foreground mb-2">
              No content found
            </h3>
            <p className="text-muted-foreground">
              No items were returned from the data source.
            </p>
          </motion.div>
        </Container>
      </section>
    );
  }

  // Render the carousel with the slides
  return (
    <CarouselImplementation
      title={title}
      description={description}
      configProp={carouselConfig}
      slides={slides as CarouselSlide[]}
      anchorId={anchorId}
    />
  );
}
