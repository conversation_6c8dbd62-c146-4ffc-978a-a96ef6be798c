import { useEffect, useState } from "react";
import {
  type DataSourceConfig,
  type GridItem,
  fetchDataSource,
} from "@/lib/data-source-fetcher";

/**
 * Hook for fetching data from a data source
 * @param dataSource The data source configuration
 * @param manualItems Optional manual items to include in the result
 * @returns An object containing the fetched items, loading state, and error state
 */
export function useDataSource(
  dataSource?: DataSourceConfig | null,
  manualItems?: GridItem[],
) {
  const [items, setItems] = useState<GridItem[]>(manualItems || []);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function loadData() {
      if (!dataSource) {
        setItems(manualItems || []);
        return;
      }

      try {
        setLoading(true);
        setError(null);
        const fetchedItems = await fetchDataSource(dataSource);
        const allItems = [...fetchedItems, ...(manualItems || [])];
        setItems(allItems);
      } catch (err) {
        console.error("Failed to load data:", err);
        setError(
          dataSource?.errorHandling?.fallbackValue || "Failed to load content",
        );
        setItems(manualItems || []);
      } finally {
        setLoading(false);
      }
    }

    loadData();
  }, [dataSource, manualItems]);

  return { items, loading, error };
}
