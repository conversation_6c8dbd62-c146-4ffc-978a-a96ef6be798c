import { defineQuery } from "next-sanity";

// Query for centers used in form selects
// Returns all necessary data including brand logo for display
export const centersForFormSelectQuery = defineQuery(`
  *[_type == 'center'] | order(name asc) {
    _id,
    name,
    centerType,
    autoscoutSellerId,
    address {
      street,
      city,
      postalCode,
      region,
      country
    },
    "brandLogo": *[_type == 'brand' && lower(name) == lower(^.centerType)][0].logo {
      asset->{
        url
      },
      altText
    }
  }
`);

// Query for consumer services only (excludes B2B services)
export const consumerServicesForFormSelectQuery = defineQuery(`
  *[_type == 'service' && serviceScope == 'consumer'] | order(title asc) {
    _id,
    title,
    'slug': slug.current,
    shortDescription,
    serviceScope
  }
`);

// Alternative query that gets all services and lets us filter client-side
// Useful if we want more control over filtering logic
export const allServicesForFormSelectQuery = defineQuery(`
  *[_type == 'service'] | order(title asc) {
    _id,
    title,
    'slug': slug.current,
    shortDescription,
    serviceScope
  }
`);
