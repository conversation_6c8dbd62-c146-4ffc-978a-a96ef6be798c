import { defineQuery } from "next-sanity";
import { pageBuilder } from "../fragments/page-builder";

export const serviceSlugsQuery =
  defineQuery(`*[_type == "service" && defined(slug.current)] {
  'params': { 'slug': slug.current }
}`);

export const serviceBySlugQuery =
  defineQuery(`*[_type == 'service' && slug.current == $slug][0] {
  _id,
  _type,
  title,
  'slug': slug.current,
  shortDescription,
  serviceScope,
  image {
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  b2b {
    serviceCategory,
    targetSegments,
    businessBenefits,
    deliveryMethod,
    responseTime,
    serviceLevel,
    pricingModel,
    implementationTime,
    integrations
  },
  ${pageBuilder},
  "seo": {
    "title": coalesce(seo.title, title, ""),
    "description": coalesce(seo.description, shortDescription, ""),
    "noIndex": seo.noIndex == true,
    "image": seo.image,
  },
}`);

export const allServicesQuery = defineQuery(`*[_type == 'service'] | order(orderRank asc, title asc) {
  _id,
  _type,
  title,
  'slug': slug.current,
  shortDescription,
  serviceScope,
  image {
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  b2b {
    serviceCategory,
    targetSegments,
    businessBenefits,
    deliveryMethod,
    responseTime,
    serviceLevel,
    pricingModel,
    implementationTime,
    integrations
  },
}`);

export const servicesPageQuery = defineQuery(`*[_type == 'servicesPage'][0] {
  _id,
  _type,
  title,
  'slug': slug.current,
  ${pageBuilder},
  "seo": {
    "title": coalesce(seo.title, title, ""),
    "description": coalesce(seo.description,  ""),
    "noIndex": seo.noIndex == true,
    "image": seo.image,
  },
}`);

// Query for services by scope (consumer or b2b)
export const servicesByScopeQuery = defineQuery(`*[_type == 'service' && serviceScope == $scope] | order(orderRank asc, title asc) {
  _id,
  _type,
  title,
  'slug': slug.current,
  shortDescription,
  serviceScope,
  image {
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  b2b {
    serviceCategory,
    targetSegments,
    businessBenefits,
    deliveryMethod,
    responseTime,
    serviceLevel,
    pricingModel,
    implementationTime,
    integrations
  },
}`);

// Query for featured services (first 6 services)
export const featuredServicesQuery = defineQuery(`*[_type == 'service'][0...6] | order(orderRank asc, title asc) {
  _id,
  _type,
  title,
  'slug': slug.current,
  shortDescription,
  serviceScope,
  image {
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  b2b {
    serviceCategory,
    targetSegments,
    businessBenefits
  },
}`);

// Query for related services (same scope, excluding current service)
export const relatedServicesQuery = defineQuery(`*[_type == 'service' && serviceScope == $serviceScope && _id != $currentServiceId][0...3] | order(orderRank asc, title asc) {
  _id,
  _type,
  title,
  'slug': slug.current,
  shortDescription,
  serviceScope,
  image {
    asset->{
      _id,
      url,
      metadata{dimensions, blurHash}
    },
    altText,
    caption,
    hotspot,
    crop
  },
  b2b {
    serviceCategory,
    targetSegments,
    businessBenefits
  },
}`);
